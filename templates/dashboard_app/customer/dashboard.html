{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}

{% block title %}Customer Dashboard - CozyWish{% endblock %}

{% block dashboard_title %}Customer Dashboard{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/customer_sidebar.html' %}
{% endblock %}

{% block dashboard_extra_css %}
<style>
    /* CozyWish Customer Dashboard - Enhanced Design System */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;
        
        /* Neutral Colors */
        --cw-neutral-100: #f5f5f5;
        --cw-neutral-200: #e5e5e5;
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;
        
        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        
        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        
        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
    }

    /* Dashboard Background */
    .dashboard-content {
        background: var(--cw-gradient-hero);
        min-height: 100vh;
        padding: 2rem;
    }

    /* Enhanced Breadcrumb */
    .breadcrumb {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 0.75rem 1rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .breadcrumb-item a {
        color: var(--cw-brand-primary);
        text-decoration: none;
        font-weight: 500;
    }

    .breadcrumb-item a:hover {
        color: var(--cw-brand-light);
    }

    .breadcrumb-item.active {
        color: var(--cw-neutral-600);
    }

    /* Enhanced Stats Cards */
    .stats-card {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
        box-shadow: var(--cw-shadow-md);
        height: 100%;
        position: relative;
        overflow: hidden;
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--cw-gradient-brand-button);
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--cw-shadow-lg);
        border-color: var(--cw-brand-primary);
    }

    .stats-card .stats-icon {
        width: 60px;
        height: 60px;
        background: var(--cw-gradient-brand-button);
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        margin-bottom: 1rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .stats-card h4 {
        font-family: var(--cw-font-heading);
        font-weight: 700;
        font-size: 2rem;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
    }

    .stats-card p {
        font-family: var(--cw-font-primary);
        color: var(--cw-neutral-600);
        margin-bottom: 0;
        font-weight: 500;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Profile Quick Edit Section */
    .profile-quick-edit {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        transition: all 0.3s ease;
    }

    .profile-quick-edit:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
    }

    .profile-completion-bar {
        height: 8px;
        background: var(--cw-neutral-200);
        border-radius: 4px;
        overflow: hidden;
        margin: 1rem 0;
    }

    .profile-completion-fill {
        height: 100%;
        background: var(--cw-gradient-brand-button);
        transition: width 0.3s ease;
        border-radius: 4px;
    }

    .verification-status {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        font-weight: 500;
        margin: 0.5rem 0;
    }

    .verification-status.verified {
        background: #f0fdf4;
        color: #166534;
        border: 1px solid #bbf7d0;
    }

    .verification-status.pending {
        background: #fffbeb;
        color: #92400e;
        border: 1px solid #fed7aa;
    }

    .verification-status.incomplete {
        background: #fef2f2;
        color: #991b1b;
        border: 1px solid #fecaca;
    }

    /* Quick Settings Toggle */
    .quick-settings {
        background: var(--cw-accent-light);
        border-radius: 0.75rem;
        padding: 1rem;
        margin: 1rem 0;
    }

    .setting-toggle {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid var(--cw-brand-accent);
    }

    .setting-toggle:last-child {
        border-bottom: none;
    }

    .toggle-switch {
        position: relative;
        width: 50px;
        height: 24px;
        background: var(--cw-neutral-200);
        border-radius: 12px;
        cursor: pointer;
        transition: background 0.3s ease;
    }

    .toggle-switch.active {
        background: var(--cw-brand-primary);
    }

    .toggle-switch::after {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        width: 20px;
        height: 20px;
        background: white;
        border-radius: 50%;
        transition: transform 0.3s ease;
        box-shadow: var(--cw-shadow-sm);
    }

    .toggle-switch.active::after {
        transform: translateX(26px);
    }

    /* Enhanced Action Buttons */
    .action-btn {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.75rem;
        color: white;
        text-decoration: none;
        transition: all 0.3s ease;
        padding: 0.75rem 1.5rem;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        font-family: var(--cw-font-heading);
        font-weight: 600;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: var(--cw-shadow-sm);
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
        color: white;
        text-decoration: none;
    }

    .action-btn.secondary {
        background: white;
        color: var(--cw-brand-primary);
        border: 2px solid var(--cw-brand-primary);
    }

    .action-btn.secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
    }

    /* Enhanced Booking Cards */
    .enhanced-booking-card {
        transition: all 0.3s ease;
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-sm);
        position: relative;
        overflow: hidden;
    }

    .enhanced-booking-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--cw-gradient-brand-button);
    }

    .enhanced-booking-card:hover {
        box-shadow: var(--cw-shadow-lg);
        border-color: var(--cw-brand-primary);
        transform: translateY(-2px);
    }

    .booking-main-info {
        padding: 1rem 1rem 1rem 1.5rem;
    }

    .booking-datetime {
        background: var(--cw-accent-light);
        padding: 0.5rem;
        border-radius: 0.5rem;
        border-left: 3px solid var(--cw-brand-primary);
        margin: 0.75rem 0;
    }

    .service-details {
        background: var(--cw-accent-light);
        padding: 0.5rem;
        border-radius: 0.5rem;
        border-left: 3px solid #6f42c1;
        margin: 0.5rem 0;
    }

    .provider-contact {
        background: #f0fdf4;
        padding: 0.5rem;
        border-radius: 0.5rem;
        border-left: 3px solid #28a745;
        margin: 0.5rem 0;
    }

    .provider-contact a {
        font-weight: 500;
        color: #166534;
        text-decoration: none;
    }

    .provider-contact a:hover {
        text-decoration: underline;
    }

    .booking-actions {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
        margin-top: 1rem;
    }

    .booking-actions .btn {
        flex: 1;
        min-width: 120px;
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
        border-radius: 0.5rem;
        font-weight: 500;
    }

    .booking-price {
        border-bottom: 2px solid #28a745;
        padding-bottom: 0.5rem;
        margin-bottom: 0.5rem;
    }

    /* Inline Edit Form */
    .inline-edit-form {
        display: none;
        background: var(--cw-accent-light);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-top: 1rem;
    }

    .inline-edit-form.active {
        display: block;
        animation: slideDown 0.3s ease;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .form-label {
        display: block;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
    }

    .form-control-cw {
        width: 100%;
        padding: 0.75rem;
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        font-family: var(--cw-font-primary);
        transition: border-color 0.3s ease;
    }

    .form-control-cw:focus {
        outline: none;
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 3px rgba(47, 22, 15, 0.1);
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        .dashboard-content {
            padding: 1rem;
        }
        
        .enhanced-booking-card .row {
            flex-direction: column;
        }
        
        .booking-actions {
            flex-direction: column;
        }
        
        .booking-actions .btn {
            width: 100%;
            margin: 0.25rem 0;
        }
        
        .stats-card {
            margin-bottom: 1rem;
        }
    }

    /* Loading States */
    .loading {
        opacity: 0.6;
        pointer-events: none;
    }

    .spinner {
        border: 2px solid var(--cw-brand-accent);
        border-top: 2px solid var(--cw-brand-primary);
        border-radius: 50%;
        width: 20px;
        height: 20px;
        animation: spin 1s linear infinite;
        display: inline-block;
        margin-right: 0.5rem;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block dashboard_content %}
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item active" aria-current="page">
            <i class="fas fa-tachometer-alt me-2"></i>
            Dashboard
        </li>
    </ol>
</nav>

<!-- Header Section with Profile Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-2" style="font-family: var(--cw-font-heading); color: var(--cw-brand-primary);">
                    Welcome back, {{ customer_profile.get_full_name|default:"Valued Customer" }}!
                </h1>
                <p class="text-muted mb-0">
                    <i class="fas fa-calendar me-1"></i>
                    {{ today|date:"l, F d, Y" }}
                </p>
            </div>
            <div class="d-flex gap-2">
                <a href="{% url 'venues_app:venue_list' %}" class="action-btn">
                    <i class="fas fa-search"></i>
                    Find Venues
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Profile Quick Edit & Stats Row -->
<div class="row mb-4">
    <!-- Profile Quick Edit Section -->
    <div class="col-lg-6 mb-4">
        <div class="profile-quick-edit">
            <div class="card-header bg-white border-0 pb-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 d-flex align-items-center">
                        <i class="fas fa-user-circle me-2 text-primary"></i>
                        Profile Quick Actions
                    </h5>
                    <button class="btn btn-sm btn-outline-primary" onclick="toggleInlineEdit()">
                        <i class="fas fa-edit me-1"></i>
                        Quick Edit
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- Profile Completion Progress -->
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="fw-bold text-primary">Profile Completion</span>
                        <span class="badge bg-primary" id="completion-percentage">{{ profile_completion_percentage|default:45 }}%</span>
                    </div>
                    <div class="profile-completion-bar">
                        <div class="profile-completion-fill" style="width: {{ profile_completion_percentage|default:45 }}%"></div>
                    </div>
                    <small class="text-muted">
                        <i class="fas fa-lightbulb me-1"></i>
                        Complete your profile to get better venue recommendations
                    </small>
                </div>

                <!-- Verification Status -->
                <div class="mb-3">
                    <div class="verification-status {% if user.is_verified %}verified{% elif profile_verification_pending %}pending{% else %}incomplete{% endif %}">
                        {% if user.is_verified %}
                            <i class="fas fa-check-circle"></i>
                            Profile Verified
                        {% elif profile_verification_pending %}
                            <i class="fas fa-clock"></i>
                            Verification Pending
                        {% else %}
                            <i class="fas fa-exclamation-triangle"></i>
                            Verification Required
                        {% endif %}
                    </div>
                    {% if not user.is_verified %}
                    <small class="text-muted d-block mt-1">
                        <i class="fas fa-shield-alt me-1"></i>
                        Verify your identity to access premium features
                    </small>
                    {% endif %}
                </div>

                <!-- Quick Profile Info Display -->
                <div class="row mb-3">
                    <div class="col-6">
                        <small class="text-muted">Email</small>
                        <div class="fw-bold">{{ user.email|truncatechars:20 }}</div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">Phone</small>
                        <div class="fw-bold">{{ customer_profile.phone_number|default:"Not provided" }}</div>
                    </div>
                </div>

                <!-- Quick Settings Toggles -->
                <div class="quick-settings">
                    <h6 class="fw-bold mb-3">
                        <i class="fas fa-cog me-2"></i>
                        Quick Settings
                    </h6>
                    
                    <div class="setting-toggle">
                        <div>
                            <div class="fw-bold">Email Notifications</div>
                            <small class="text-muted">Booking updates & promotions</small>
                        </div>
                        <div class="toggle-switch {% if user_preferences.email_notifications %}active{% endif %}" 
                             data-setting="email_notifications"></div>
                    </div>
                    
                    <div class="setting-toggle">
                        <div>
                            <div class="fw-bold">SMS Reminders</div>
                            <small class="text-muted">Appointment reminders via text</small>
                        </div>
                        <div class="toggle-switch {% if user_preferences.sms_reminders %}active{% endif %}" 
                             data-setting="sms_reminders"></div>
                    </div>
                    
                    <div class="setting-toggle">
                        <div>
                            <div class="fw-bold">Marketing Updates</div>
                            <small class="text-muted">Special offers & new venues</small>
                        </div>
                        <div class="toggle-switch {% if user_preferences.marketing_updates %}active{% endif %}" 
                             data-setting="marketing_updates"></div>
                    </div>
                </div>

                <!-- Inline Edit Form -->
                <div class="inline-edit-form" id="inline-edit-form">
                    <h6 class="fw-bold mb-3">
                        <i class="fas fa-edit me-2"></i>
                        Edit Profile Information
                    </h6>
                    <form id="quick-profile-form">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">First Name</label>
                                    <input type="text" class="form-control-cw" name="first_name" 
                                           value="{{ customer_profile.first_name }}" placeholder="Your first name">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Last Name</label>
                                    <input type="text" class="form-control-cw" name="last_name" 
                                           value="{{ customer_profile.last_name }}" placeholder="Your last name">
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Phone Number</label>
                            <input type="tel" class="form-control-cw" name="phone_number" 
                                   value="{{ customer_profile.phone_number }}" placeholder="+****************">
                        </div>
                        <div class="d-flex gap-2 justify-content-end">
                            <button type="button" class="btn btn-secondary" onclick="cancelInlineEdit()">
                                <i class="fas fa-times me-1"></i>Cancel
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="col-lg-6">
        <div class="row">
            <div class="col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <h4>{{ total_bookings|default:0 }}</h4>
                    <p>Total Bookings</p>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h4>{{ pending_bookings|default:0 }}</h4>
                    <p>Pending Bookings</p>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h4>{{ favorite_venues_count|default:0 }}</h4>
                    <p>Favorite Venues</p>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h4>{{ reviews_written|default:0 }}</h4>
                    <p>Reviews Written</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Row -->
<div class="row">
    <!-- Enhanced Upcoming Bookings -->
    <div class="col-lg-8 mb-4">
        <div class="card h-100" style="border: 1px solid var(--cw-brand-accent); border-radius: 1rem; box-shadow: var(--cw-shadow-md);">
            <div class="card-header d-flex justify-content-between align-items-center bg-white border-0">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-alt me-2 text-primary"></i>
                    Upcoming Bookings
                </h5>
                <a href="{% url 'dashboard_app:customer_booking_status' %}?date=upcoming" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-list me-1"></i>View All
                </a>
            </div>
            <div class="card-body">
                {% if upcoming_bookings %}
                    {% for booking in upcoming_bookings %}
                    <div class="enhanced-booking-card mb-3">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="booking-main-info">
                                    <h6 class="mb-1 fw-bold">{{ booking.venue.venue_name }}</h6>
                                    <div class="booking-datetime mb-2">
                                        <small class="text-primary fw-bold">
                                            <i class="fas fa-calendar me-1"></i>
                                            {{ booking.booking_date|date:"M d, Y" }}
                                            <i class="fas fa-clock ms-2 me-1"></i>
                                            {% for item in booking.items.all|slice:":1" %}
                                                {{ item.scheduled_time|time:"g:i A" }}
                                            {% endfor %}
                                        </small>
                                    </div>
                                    
                                    <!-- Service Details -->
                                    <div class="service-details mb-2">
                                        <small class="text-purple fw-bold">
                                            <i class="fas fa-spa me-1"></i>
                                            {% for item in booking.items.all|slice:":2" %}
                                                {{ item.service_title }}{% if not forloop.last %}, {% endif %}
                                            {% endfor %}
                                            {% if booking.items.count > 2 %}
                                                <span class="text-muted">+{{ booking.items.count|add:"-2" }} more</span>
                                            {% endif %}
                                        </small>
                                    </div>
                                    
                                    <!-- Provider Contact Info -->
                                    {% if booking.venue.service_provider %}
                                    <div class="provider-contact mb-2">
                                        <small class="text-success fw-bold">
                                            <i class="fas fa-user-tie me-1"></i>
                                            {{ booking.venue.service_provider.business_name|default:booking.venue.service_provider.contact_person_name }}
                                            {% if booking.venue.service_provider.business_phone_number %}
                                                <a href="tel:{{ booking.venue.service_provider.business_phone_number }}" 
                                                   class="text-success text-decoration-none ms-2"
                                                   title="Call provider">
                                                    <i class="fas fa-phone"></i> {{ booking.venue.service_provider.business_phone_number }}
                                                </a>
                                            {% endif %}
                                        </small>
                                    </div>
                                    {% endif %}
                                    
                                    <span class="badge bg-{% if booking.status == 'confirmed' %}success{% elif booking.status == 'pending' %}warning{% else %}secondary{% endif %}">
                                        <i class="fas fa-{% if booking.status == 'confirmed' %}check-circle{% elif booking.status == 'pending' %}clock{% else %}question-circle{% endif %} me-1"></i>
                                        {{ booking.get_status_display }}
                                    </span>
                                </div>
                            </div>
                            
                            <div class="col-md-3 text-center">
                                <div class="booking-price mb-2">
                                    <strong class="h5 text-success">${{ booking.total_price }}</strong>
                                </div>
                                <div class="booking-location">
                                    <small class="text-muted">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        {{ booking.venue.city }}, {{ booking.venue.state }}
                                    </small>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="booking-actions">
                                    <a href="{% url 'booking_cart_app:booking_detail' booking.id %}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye me-1"></i>View
                                    </a>
                                    {% if booking.status == 'confirmed' %}
                                    <button class="btn btn-sm btn-outline-warning" 
                                            onclick="requestReschedule({{ booking.id }})">
                                        <i class="fas fa-calendar-alt me-1"></i>Reschedule
                                    </button>
                                    {% endif %}
                                    {% if booking.status == 'pending' %}
                                    <button class="btn btn-sm btn-outline-danger" 
                                            onclick="cancelBooking({{ booking.id }})">
                                        <i class="fas fa-times me-1"></i>Cancel
                                    </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-calendar-times fa-3x text-muted"></i>
                    </div>
                    <h6 class="text-muted">No upcoming bookings</h6>
                    <p class="text-muted mb-3">Discover amazing spa and wellness venues near you</p>
                    <a href="{% url 'venues_app:venue_list' %}" class="action-btn">
                        <i class="fas fa-search me-2"></i>Find Venues
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Sidebar Content -->
    <div class="col-lg-4">
        <!-- Favorite Venues Quick Access -->
        <div class="card mb-4" style="border: 1px solid var(--cw-brand-accent); border-radius: 1rem; box-shadow: var(--cw-shadow-md);">
            <div class="card-header d-flex justify-content-between align-items-center bg-white border-0">
                <h5 class="mb-0">
                    <i class="fas fa-heart me-2 text-danger"></i>
                    Favorite Venues
                </h5>
                <a href="{% url 'dashboard_app:customer_favorite_venues' %}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-list me-1"></i>View All
                </a>
            </div>
            <div class="card-body">
                {% if recent_favorites %}
                    {% for favorite in recent_favorites|slice:":3" %}
                    <div class="d-flex align-items-center mb-3 p-2 border rounded" style="border-color: var(--cw-brand-accent) !important;">
                        <div class="flex-shrink-0 me-3">
                            {% if favorite.venue.images.first %}
                                <img src="{{ favorite.venue.images.first.image.url }}" 
                                     alt="{{ favorite.venue.venue_name }}" 
                                     class="rounded" style="width: 50px; height: 50px; object-fit: cover;">
                            {% else %}
                                <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                     style="width: 50px; height: 50px;">
                                    <i class="fas fa-spa text-muted"></i>
                                </div>
                            {% endif %}
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ favorite.venue.venue_name|truncatechars:20 }}</h6>
                            <small class="text-muted">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                {{ favorite.venue.city }}
                            </small>
                        </div>
                        <div>
                            <a href="{% url 'venues_app:venue_detail' favorite.venue.id %}" 
                               class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-heart-broken fa-2x text-muted mb-2"></i>
                    <p class="text-muted mb-0">No favorite venues yet</p>
                    <small class="text-muted">Explore venues and save your favorites</small>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Quick Actions Card -->
        <div class="card" style="border: 1px solid var(--cw-brand-accent); border-radius: 1rem; box-shadow: var(--cw-shadow-md);">
            <div class="card-header bg-white border-0">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2 text-warning"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'venues_app:venue_list' %}" class="action-btn">
                        <i class="fas fa-search me-2"></i>Find Venues
                    </a>
                    <a href="{% url 'dashboard_app:customer_booking_status' %}" class="action-btn secondary">
                        <i class="fas fa-history me-2"></i>Booking History
                    </a>
                    <a href="{% url 'accounts_app:customer_profile' %}" class="action-btn secondary">
                        <i class="fas fa-user me-2"></i>Full Profile
                    </a>
                    <a href="{% url 'review_app:customer_review_list' %}" class="action-btn secondary">
                        <i class="fas fa-star me-2"></i>My Reviews
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block dashboard_extra_js %}
<script>
// Enhanced Customer Dashboard JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
});

function initializeDashboard() {
    // Initialize toggle switches
    document.querySelectorAll('.toggle-switch').forEach(function(toggle) {
        toggle.addEventListener('click', function() {
            toggleSetting(this);
        });
    });

    // Initialize inline edit form
    setupInlineEdit();
    
    // Add loading states to action buttons
    setupActionButtons();
}

function toggleInlineEdit() {
    const form = document.getElementById('inline-edit-form');
    const isActive = form.classList.contains('active');
    
    if (isActive) {
        cancelInlineEdit();
    } else {
        form.classList.add('active');
        form.querySelector('input[name="first_name"]').focus();
    }
}

function cancelInlineEdit() {
    const form = document.getElementById('inline-edit-form');
    form.classList.remove('active');
    
    // Reset form to original values
    form.querySelectorAll('input').forEach(function(input) {
        input.value = input.defaultValue;
    });
}

function setupInlineEdit() {
    const form = document.getElementById('quick-profile-form');
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            submitQuickProfile();
        });
    }
}

function submitQuickProfile() {
    const form = document.getElementById('quick-profile-form');
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    
    // Add loading state
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<span class="spinner"></span>Saving...';
    submitBtn.disabled = true;
    
    fetch('{% url "dashboard_app:customer_profile_quick_update" %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Profile updated successfully!', 'success');
            cancelInlineEdit();
            
            // Update profile completion percentage if provided
            if (data.completion_percentage) {
                updateProfileCompletion(data.completion_percentage);
            }
            
            // Refresh page to show updated data
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification(data.message || 'Error updating profile', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Network error occurred', 'error');
    })
    .finally(() => {
        // Restore button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

function toggleSetting(element) {
    const setting = element.dataset.setting;
    const isActive = element.classList.contains('active');
    
    // Toggle visual state immediately for responsiveness
    element.classList.toggle('active');
    
    // Send update to server
    fetch('{% url "dashboard_app:customer_settings_toggle" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            setting: setting,
            value: !isActive
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`${setting.replace('_', ' ')} ${!isActive ? 'enabled' : 'disabled'}`, 'success');
        } else {
            // Revert visual state if server update failed
            element.classList.toggle('active');
            showNotification(data.message || 'Error updating setting', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        // Revert visual state if network error
        element.classList.toggle('active');
        showNotification('Network error occurred', 'error');
    });
}

function updateProfileCompletion(percentage) {
    const progressBar = document.querySelector('.profile-completion-fill');
    const percentageDisplay = document.getElementById('completion-percentage');
    
    if (progressBar && percentageDisplay) {
        progressBar.style.width = percentage + '%';
        percentageDisplay.textContent = percentage + '%';
    }
}

function requestReschedule(bookingId) {
    if (confirm('Would you like to request a reschedule for this booking?')) {
        showNotification('Reschedule request feature coming soon!', 'info');
        // TODO: Implement reschedule functionality
    }
}

function cancelBooking(bookingId) {
    if (confirm('Are you sure you want to cancel this booking?')) {
        showNotification('Booking cancellation feature coming soon!', 'info');
        // TODO: Implement cancellation functionality
    }
}

function setupActionButtons() {
    document.querySelectorAll('.action-btn').forEach(function(btn) {
        if (btn.tagName === 'BUTTON') {
            btn.addEventListener('click', function() {
                if (!this.disabled) {
                    this.classList.add('loading');
                }
            });
        }
    });
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px;';
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
