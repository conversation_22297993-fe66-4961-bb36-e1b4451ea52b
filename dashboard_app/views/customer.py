"""Customer dashboard views."""

# --- Standard Library Imports ---
import csv
from datetime import date, timedelta
from decimal import Decimal
import logging

# --- Third-Party Imports ---
from django.conf import settings
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.core.cache import cache
from django.core.paginator import Paginator
from django.db import transaction
from django.db.models import Avg, Count, Q, Sum
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse_lazy
from django.utils import timezone
from django.views import View
from django.views.decorators.http import require_http_methods
from django.views.generic import DetailView, ListView


# --- Local App Imports ---
from accounts_app.models import (
    CustomUser,
    CustomerProfile,
    ServiceProviderProfile,
    TeamMember,
)
from booking_cart_app.models import Booking, BookingItem
from venues_app.models import Service, Venue
from ..forms import DateRangeForm
from ..helpers import get_date_range, get_int_param, get_valid_param
from ..logging_utils import (
    log_admin_dashboard_access,
    log_admin_data_access,
    log_analytics_access,
    log_booking_status_access,
    log_dashboard_access,
    log_dashboard_activity,
    log_dashboard_filter_usage,
    log_error,
    log_favorite_venue_event,
    log_profile_access,
    log_provider_dashboard_activity,
    log_provider_earnings_access,
    log_provider_service_stats_access,
    log_security_event,
    log_system_health_check,
    performance_monitor,
)
from ..decorators import customer_required, provider_required, staff_required
from ..mixins import ProviderRequiredMixin
from ..models import FavoriteVenue, UserPreferences
from ..constants import (
    CUSTOMER_ONLY_ERROR,
    PROVIDER_ONLY_ERROR,
    ADMIN_ONLY_ERROR,
    FAVORITE_ADDED_SUCCESS,
    FAVORITE_REMOVED_SUCCESS,
    FAVORITE_ALREADY_EXISTS,
    DASHBOARD_ACCESS_SUCCESS,
    VENUE_NOT_FOUND_ERROR,
    PERMISSION_DENIED_ERROR,
)


# --- Customer Dashboard Views ---
@customer_required
@performance_monitor("customer_dashboard_access")
def customer_dashboard_view(request):
    """
    Main customer dashboard view showing overview of bookings, favorites, and profile.
    """
    # Role check handled by customer_required decorator

    try:
        # Get customer profile
        customer_profile, created = CustomerProfile.objects.get_or_create(user=request.user)

        # Calculate profile completion percentage
        profile_completion_percentage = calculate_profile_completion(customer_profile)

        # Get user preferences
        user_preferences, created = UserPreferences.objects.get_or_create(
            user=request.user,
            defaults={
                'email_notifications': True,
                'sms_reminders': True,
                'marketing_updates': False
            }
        )

        # Profile verification status
        profile_verification_pending = False  # TODO: Implement verification system
        is_verified = getattr(request.user, 'is_verified', False)

        # Get recent bookings (last 5)
        recent_bookings = Booking.objects.filter(
            customer=request.user
        ).order_by('-booking_date')[:5]

        # Get upcoming bookings with enhanced information
        today = timezone.now().date()
        upcoming_bookings = Booking.objects.filter(
            customer=request.user,
            status__in=['pending', 'confirmed']
        ).select_related(
            'venue', 
            'venue__service_provider'
        ).prefetch_related(
            'items', 
            'items__service'
        ).order_by('booking_date')[:5]

        # Get favorite venues count and recent favorites
        favorite_venues_count = FavoriteVenue.objects.filter(customer=request.user).count()
        recent_favorites = FavoriteVenue.objects.filter(
            customer=request.user
        ).select_related('venue').order_by('-created_at')[:5]

        # Get booking statistics
        total_bookings = Booking.objects.filter(customer=request.user).count()
        pending_bookings = Booking.objects.filter(
            customer=request.user,
            status='pending'
        ).count()
        confirmed_bookings = Booking.objects.filter(
            customer=request.user,
            status='confirmed'
        ).count()
        completed_bookings = Booking.objects.filter(
            customer=request.user,
            status='completed'
        ).count()

        # Get reviews written count
        try:
            from review_app.models import Review
            reviews_written = Review.objects.filter(customer=request.user).count()
        except ImportError:
            reviews_written = 0

        # Payment history summary (optional enhancement)
        try:
            total_spent = Booking.objects.filter(
                customer=request.user,
                status='completed'
            ).aggregate(total=Sum('total_price'))['total'] or 0
        except:
            total_spent = 0

        # Get recommended venues based on favorites and past bookings
        recommended_venues = []
        if favorite_venues_count > 0:
            # Get categories from favorite venues
            favorite_categories = FavoriteVenue.objects.filter(
                customer=request.user
            ).values_list('venue__categories', flat=True)
            
            # Find similar venues (simple recommendation)
            try:
                recommended_venues = Venue.objects.filter(
                    categories__in=favorite_categories,
                    approval_status='approved',
                    visibility='active'
                ).exclude(
                    id__in=FavoriteVenue.objects.filter(
                        customer=request.user
                    ).values_list('venue_id', flat=True)
                ).distinct()[:3]
            except:
                recommended_venues = []

        # Log dashboard access
        log_dashboard_access(
            user=request.user,
            dashboard_type='customer',
            request=request,
            additional_details={
                'total_bookings': total_bookings,
                'favorite_venues_count': favorite_venues_count,
                'profile_completion': profile_completion_percentage
            }
        )

        context = {
            'customer_profile': customer_profile,
            'profile_completion_percentage': profile_completion_percentage,
            'user_preferences': user_preferences,
            'profile_verification_pending': profile_verification_pending,
            'is_verified': is_verified,
            'recent_bookings': recent_bookings,
            'upcoming_bookings': upcoming_bookings,
            'favorite_venues_count': favorite_venues_count,
            'recent_favorites': recent_favorites,
            'total_bookings': total_bookings,
            'pending_bookings': pending_bookings,
            'confirmed_bookings': confirmed_bookings,
            'completed_bookings': completed_bookings,
            'reviews_written': reviews_written,
            'total_spent': total_spent,
            'recommended_venues': recommended_venues,
            'today': timezone.now().date(),
        }

        return render(request, 'dashboard_app/customer/dashboard.html', context)

    except Exception as e:
        log_error(
            error_type='customer_dashboard_access',
            error_message="Error loading customer dashboard",
            user=request.user,
            request=request,
            exception=e
        )
        messages.error(request, 'There was an error loading your dashboard. Please try again.')
        return redirect('venues_app:home')


@customer_required
@performance_monitor("customer_booking_status_access")
def customer_booking_status_view(request):
    """
    Customer booking status view showing all bookings with filtering options.
    """
    # Role check handled by customer_required decorator

    try:
        # Get filter parameters with validation
        status_filter = get_valid_param(
            request,
            'status',
            {'all', 'pending', 'confirmed', 'completed', 'cancelled'},
            'all'
        )
        date_filter = get_valid_param(
            request,
            'date',
            {'all', 'upcoming', 'past'},
            'all'
        )

        # Base queryset
        bookings = Booking.objects.filter(customer=request.user)

        # Apply status filter
        if status_filter != 'all':
            bookings = bookings.filter(status=status_filter)

        # Apply date filter
        today = timezone.now().date()
        if date_filter == 'upcoming':
            bookings = bookings.filter(booking_date__gte=today)
        elif date_filter == 'past':
            bookings = bookings.filter(booking_date__lt=today)

        # Order by booking date (most recent first)
        bookings = bookings.order_by('-booking_date')

        # Pagination
        paginator = Paginator(bookings, 10)  # Show 10 bookings per page
        page_number = get_int_param(request, 'page', 1)
        page_obj = paginator.get_page(page_number)

        # Get booking counts for filter display
        total_count = Booking.objects.filter(customer=request.user).count()
        pending_count = Booking.objects.filter(customer=request.user, status='pending').count()
        confirmed_count = Booking.objects.filter(customer=request.user, status='confirmed').count()
        completed_count = Booking.objects.filter(customer=request.user, status='completed').count()
        cancelled_count = Booking.objects.filter(customer=request.user, status='cancelled').count()

        # Log booking status access with filter usage
        log_booking_status_access(
            user=request.user,
            booking_count=bookings.count(),
            filter_type=f"{status_filter}_{date_filter}",
            request=request
        )

        # Log filter usage for analytics
        if status_filter != 'all' or date_filter != 'all':
            log_dashboard_filter_usage(
                user=request.user,
                dashboard_section='customer_booking_status',
                filters_applied={
                    'status_filter': status_filter,
                    'date_filter': date_filter
                },
                results_count=bookings.count(),
                request=request
            )

        context = {
            'page_obj': page_obj,
            'bookings': page_obj.object_list,
            'status_filter': status_filter,
            'date_filter': date_filter,
            'total_count': total_count,
            'pending_count': pending_count,
            'confirmed_count': confirmed_count,
            'completed_count': completed_count,
            'cancelled_count': cancelled_count,
        }

        return render(request, 'dashboard_app/customer/booking_status.html', context)

    except Exception as e:
        log_error(
            error_type='booking_status_access',
            error_message="Error loading customer booking status",
            user=request.user,
            request=request,
            exception=e
        )
        messages.error(request, 'There was an error loading your bookings. Please try again.')
        return redirect('dashboard_app:customer_dashboard')


@customer_required
@performance_monitor("customer_profile_edit_redirect")
def customer_profile_edit_view(request):
    """
    Customer profile edit view - redirects to accounts_app profile edit.
    """
    # Role check handled by customer_required decorator

    # Log profile access
    log_profile_access(
        user=request.user,
        access_type='edit_redirect',
        request=request
    )

    # Redirect to accounts_app profile edit
    return redirect('accounts_app:customer_profile_edit')


@customer_required
@require_http_methods(["POST"])
@performance_monitor("customer_profile_quick_update")
def customer_profile_quick_update(request):
    """
    Handle quick profile updates from dashboard inline editing.
    """
    try:
        # Get or create customer profile
        customer_profile, created = CustomerProfile.objects.get_or_create(user=request.user)
        
        # Extract form data
        first_name = request.POST.get('first_name', '').strip()
        last_name = request.POST.get('last_name', '').strip()
        phone_number = request.POST.get('phone_number', '').strip()
        
        # Validate and update fields
        updated_fields = []
        
        if first_name != customer_profile.first_name:
            customer_profile.first_name = first_name
            updated_fields.append('first_name')
            
        if last_name != customer_profile.last_name:
            customer_profile.last_name = last_name
            updated_fields.append('last_name')
            
        if phone_number != customer_profile.phone_number:
            customer_profile.phone_number = phone_number
            updated_fields.append('phone_number')
        
        # Save changes if any
        if updated_fields:
            customer_profile.save(update_fields=updated_fields)
            
            # Log profile changes
            log_profile_access(
                user=request.user,
                access_type='quick_update',
                request=request,
                additional_details={'updated_fields': updated_fields}
            )
        
        # Calculate profile completion percentage
        completion_percentage = calculate_profile_completion(customer_profile)
        
        return JsonResponse({
            'success': True,
            'message': 'Profile updated successfully!',
            'completion_percentage': completion_percentage,
            'updated_fields': updated_fields
        })
        
    except Exception as e:
        log_error(
            error_type='customer_profile_quick_update',
            error_message="Error during quick profile update",
            user=request.user,
            request=request,
            exception=e
        )
        return JsonResponse({
            'success': False,
            'message': 'Error updating profile. Please try again.'
        }, status=400)


@customer_required
@require_http_methods(["POST"])
@performance_monitor("customer_settings_toggle")
def customer_settings_toggle(request):
    """
    Handle quick settings toggles from dashboard.
    """
    try:
        import json
        data = json.loads(request.body)
        
        setting = data.get('setting')
        value = data.get('value', False)
        
        # Get or create user preferences
        user_preferences, created = UserPreferences.objects.get_or_create(
            user=request.user,
            defaults={
                'email_notifications': True,
                'sms_reminders': True,
                'marketing_updates': False
            }
        )
        
        # Update the specific setting
        if setting == 'email_notifications':
            user_preferences.email_notifications = value
        elif setting == 'sms_reminders':
            user_preferences.sms_reminders = value
        elif setting == 'marketing_updates':
            user_preferences.marketing_updates = value
        else:
            return JsonResponse({
                'success': False,
                'message': 'Invalid setting'
            }, status=400)
        
        user_preferences.save()
        
        # Log setting change
        log_profile_access(
            user=request.user,
            access_type='settings_toggle',
            request=request,
            additional_details={
                'setting': setting,
                'value': value
            }
        )
        
        return JsonResponse({
            'success': True,
            'message': f'Setting {setting} updated successfully'
        })
        
    except Exception as e:
        log_error(
            error_type='customer_settings_toggle',
            error_message="Error during settings toggle",
            user=request.user,
            request=request,
            exception=e
        )
        return JsonResponse({
            'success': False,
            'message': 'Error updating setting. Please try again.'
        }, status=400)


def calculate_profile_completion(customer_profile):
    """
    Calculate profile completion percentage based on filled fields.
    """
    total_fields = 8  # Adjust based on important profile fields
    completed_fields = 0
    
    # Check various profile fields
    if customer_profile.first_name:
        completed_fields += 1
    if customer_profile.last_name:
        completed_fields += 1
    if customer_profile.phone_number:
        completed_fields += 1
    if customer_profile.address:
        completed_fields += 1
    if customer_profile.city:
        completed_fields += 1
    if customer_profile.zip_code:
        completed_fields += 1
    if customer_profile.profile_picture:
        completed_fields += 1
    if customer_profile.birth_month and customer_profile.birth_year:
        completed_fields += 1
    
    return int((completed_fields / total_fields) * 100)


@customer_required
@performance_monitor("customer_favorite_venues_access")
def customer_favorite_venues_view(request):
    """
    Customer favorite venues view showing all favorited venues.
    """
    # Role check handled by customer_required decorator

    try:
        # Get favorite venues with related venue data
        favorite_venues = FavoriteVenue.objects.filter(
            customer=request.user
        ).select_related('venue').order_by('-added_date')

        # Pagination
        paginator = Paginator(favorite_venues, 12)  # Show 12 venues per page
        page_number = get_int_param(request, 'page', 1)
        page_obj = paginator.get_page(page_number)

        # Log favorites access
        log_dashboard_activity(
            activity_type='favorite_venues_access',
            user=request.user,
            request=request,
            details={
                'favorite_count': favorite_venues.count()
            }
        )

        context = {
            'page_obj': page_obj,
            'favorite_venues': page_obj.object_list,
            'total_favorites': favorite_venues.count(),
        }

        return render(request, 'dashboard_app/customer/favorite_venues.html', context)

    except Exception as e:
        log_error(
            error_type='favorite_venues_access',
            error_message="Error loading customer favorite venues",
            user=request.user,
            request=request,
            exception=e
        )
        messages.error(request, 'There was an error loading your favorite venues. Please try again.')
        return redirect('dashboard_app:customer_dashboard')


@customer_required
@require_http_methods(["POST"])
@performance_monitor("add_favorite_venue")
def add_favorite_venue_view(request, venue_id):
    """
    Add a venue to customer's favorites via AJAX.
    """
    # Role check handled by customer_required decorator

    try:
        # Get the venue
        try:
            venue = Venue.objects.get(id=venue_id, approval_status='approved', visibility='active')
        except Venue.DoesNotExist:
            log_error(
                error_type='venue_not_found',
                error_message=f"Venue with id {venue_id} not found for favorite add",
                user=request.user,
                request=request,
                details={'venue_id': venue_id}
            )
            return JsonResponse({
                'success': False,
                'message': VENUE_NOT_FOUND_ERROR
            }, status=404)

        # Check if already favorited with locking
        with transaction.atomic():
            favorite, created = FavoriteVenue.objects.select_for_update().get_or_create(
                customer=request.user,
                venue=venue
            )

        if created:
            # Log favorite added
            log_favorite_venue_event(
                event_type='added',
                user=request.user,
                venue_name=venue.venue_name,
                venue_id=venue.id,
                request=request
            )

            return JsonResponse({
                'success': True,
                'message': FAVORITE_ADDED_SUCCESS,
                'action': 'added'
            })
        else:
            return JsonResponse({
                'success': False,
                'message': FAVORITE_ALREADY_EXISTS,
                'action': 'exists'
            })

    except Exception as e:
        log_error(
            error_type='favorite_add',
            error_message="Error adding venue to favorites",
            user=request.user,
            request=request,
            exception=e,
            details={'venue_id': venue_id}
        )
        return JsonResponse({
            'success': False,
            'message': 'There was an error adding the venue to your favorites. Please try again.'
        }, status=500)


@customer_required
@require_http_methods(["POST"])
@performance_monitor("remove_favorite_venue")
def remove_favorite_venue_view(request, venue_id):
    """
    Remove a venue from customer's favorites via AJAX.
    """
    # Role check handled by customer_required decorator

    try:
        # Get the venue
        try:
            venue = Venue.objects.get(id=venue_id)
        except Venue.DoesNotExist:
            log_error(
                error_type='venue_not_found',
                error_message=f"Venue with id {venue_id} not found for favorite remove",
                user=request.user,
                request=request,
                details={'venue_id': venue_id}
            )
            return JsonResponse({
                'success': False,
                'message': VENUE_NOT_FOUND_ERROR
            }, status=404)

        # Try to remove from favorites
        try:
            with transaction.atomic():
                favorite = FavoriteVenue.objects.select_for_update().get(
                    customer=request.user,
                    venue=venue
                )
                favorite.delete()

            # Log favorite removed
            log_favorite_venue_event(
                event_type='removed',
                user=request.user,
                venue_name=venue.venue_name,
                venue_id=venue.id,
                request=request
            )

            return JsonResponse({
                'success': True,
                'message': FAVORITE_REMOVED_SUCCESS,
                'action': 'removed'
            })

        except FavoriteVenue.DoesNotExist:
            return JsonResponse({
                'success': False,
                'message': 'This venue is not in your favorites.',
                'action': 'not_found'
            })

    except Exception as e:
        log_error(
            error_type='favorite_remove',
            error_message="Error removing venue from favorites",
            user=request.user,
            request=request,
            exception=e,
            details={'venue_id': venue_id}
        )
        return JsonResponse({
            'success': False,
            'message': 'There was an error removing the venue from your favorites. Please try again.'
        }, status=500)


@customer_required
@performance_monitor("check_favorite_status")
def check_favorite_status_view(request, venue_id):
    """
    Check if a venue is in customer's favorites (AJAX endpoint).
    """
    # Role check handled by customer_required decorator

    try:
        # Check if venue exists and is favorited
        try:
            venue = Venue.objects.get(id=venue_id)
        except Venue.DoesNotExist:
            return JsonResponse({
                'success': False,
                'is_favorite': False,
                'message': VENUE_NOT_FOUND_ERROR
            }, status=404)
        is_favorite = FavoriteVenue.objects.filter(
            customer=request.user,
            venue=venue
        ).exists()

        return JsonResponse({
            'success': True,
            'is_favorite': is_favorite,
            'venue_id': venue_id
        })

    except Exception as e:
        log_error(
            error_type='favorite_status_check',
            error_message="Error checking favorite status",
            user=request.user,
            request=request,
            exception=e,
            details={'venue_id': venue_id}
        )
        return JsonResponse({
            'success': False,
            'is_favorite': False,
            'message': 'There was an error checking the favorite status.'
        }, status=500)


@customer_required
@performance_monitor("customer_booking_reschedule_request")
def customer_booking_reschedule_request(request):
    """
    Handle booking reschedule requests from the enhanced dashboard.
    """
    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allowed'}, status=405)
    
    try:
        booking_slug = request.POST.get('booking_slug')
        new_date = request.POST.get('new_date')
        new_time = request.POST.get('new_time')
        reschedule_reason = request.POST.get('reschedule_reason', '')
        
        # Validate required fields
        if not all([booking_slug, new_date, new_time]):
            return JsonResponse({'error': 'Missing required fields'}, status=400)
        
        # Get the booking
        booking = get_object_or_404(
            Booking,
            slug=booking_slug,
            customer=request.user,
            status__in=['pending', 'confirmed']
        )
        
        # Parse new datetime
        try:
            new_datetime = timezone.datetime.strptime(f"{new_date} {new_time}", "%Y-%m-%d %H:%M")
            new_datetime = timezone.make_aware(new_datetime)
        except ValueError:
            return JsonResponse({'error': 'Invalid date or time format'}, status=400)
        
        # Check if new date is in the future
        if new_datetime <= timezone.now():
            return JsonResponse({'error': 'New booking time must be in the future'}, status=400)
        
        # Check if new date is at least 24 hours from now
        if new_datetime < timezone.now() + timezone.timedelta(hours=24):
            return JsonResponse({'error': 'New booking time must be at least 24 hours from now'}, status=400)
        
        # Create a reschedule request (you might want to create a separate model for this)
        # For now, we'll add a note to the booking and update status if needed
        
        # Log the reschedule request
        log_dashboard_activity(
            activity_type='booking_reschedule_request',
            user=request.user,
            request=request,
            details={
                'booking_id': str(booking.booking_id),
                'original_date': str(booking.booking_date),
                'requested_date': new_date,
                'requested_time': new_time,
                'reason': reschedule_reason,
                'venue_name': booking.venue.venue_name
            }
        )
        
        # In a real implementation, you might:
        # 1. Create a RescheduleRequest model entry
        # 2. Send notification to the service provider
        # 3. Update booking status to 'reschedule_requested'
        # 4. Send email notifications
        
        # For now, we'll return success
        return JsonResponse({
            'success': True,
            'message': 'Reschedule request submitted successfully! The provider will be notified and will respond within 24 hours.',
            'booking_id': str(booking.booking_id)
        })
        
    except Booking.DoesNotExist:
        return JsonResponse({'error': 'Booking not found or you do not have permission to reschedule it'}, status=404)
    except Exception as e:
        log_error(
            error_type='booking_reschedule_request_error',
            error_message=f"Error processing reschedule request: {str(e)}",
            user=request.user,
            request=request,
            exception=e
        )
        return JsonResponse({'error': 'An error occurred while processing your request'}, status=500)


