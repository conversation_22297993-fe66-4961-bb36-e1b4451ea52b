from django.test import TestCase
from django.contrib.auth import get_user_model
from django.test import Client
from bs4 import BeautifulSoup


class AccessibilityTests(TestCase):
    def setUp(self):
        self.client = Client()
        User = get_user_model()
        self.user = User.objects.create_user(
            email='<EMAIL>', password='pass', role=User.SERVICE_PROVIDER
        )
        from accounts_app.models import ServiceProviderProfile
        profile = ServiceProviderProfile.objects.create(
            user=self.user,
            business_name='Biz',
            business_phone_number='+**********',
            business_address='123 St',
            city='City',
            state='CA',
            zip_code='12345',
        )
        from venues_app.models import Venue
        self.venue = Venue.objects.create(
            service_provider=profile,
            venue_name='Test',
            short_description='desc',
            state='ST',
            county='County',
            city='City',
            street_number='1',
            street_name='Street',
            operating_hours='9-5',
            approval_status='approved',
            visibility='active'
        )

    def test_provider_earnings_form_has_aria_labels(self):
        self.client.login(email='<EMAIL>', password='pass')
        response = self.client.get('/dashboard/provider/earnings-reports/', secure=True)
        self.assertIn(response.status_code, [200, 302])
        if response.status_code != 200:
            return
        soup = BeautifulSoup(response.content, 'html.parser')
        form = soup.find('form')
        self.assertIsNotNone(form)
        self.assertTrue(form.has_attr('aria-label'))
        for field in form.find_all(['select', 'input']):
            self.assertTrue(field.has_attr('aria-label') or field.has_attr('aria-describedby'))
